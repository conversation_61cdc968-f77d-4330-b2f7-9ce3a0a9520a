# <PERSON> - Professional Website

A clean, professional, single-page website for <PERSON> that fulfills Meta's Business Verification requirements for the WhatsApp API.

## 🌟 Features

- **Professional Design**: Clean, modern layout with responsive design
- **Light/Dark Theme**: Toggle between light and dark modes
- **Business Verification Ready**: Includes all required metadata and structured data for Meta Business Verification
- **Accessibility Compliant**: Semantic HTML, ARIA labels, skip links, and keyboard navigation
- **SEO Optimized**: Comprehensive meta tags, Open Graph, and JSON-LD structured data
- **Progressive Enhancement**: Works without JavaScript (graceful degradation)
- **Performance Optimized**: Built with Next.js 15 and optimized for speed

## 📋 Sections

1. **Header/Hero Section**: <PERSON> branding, profile photo placeholder, and professional description
2. **Certifications**: ISC2 Certified in Cybersecurity (CC) badge with Credly integration
3. **Social Media**: Links to GitHub, LinkedIn, X (Twitter), and email with professional icons
4. **Footer**: Business information including IČO, address, and contact details

## 🛠 Tech Stack

- **Framework**: Next.js 15.3.3 with React 19
- **Styling**: Tailwind CSS v4 with shadcn/ui components
- **Icons**: Lucide React
- **Fonts**: Geist Sans and Geist Mono
- **Language**: TypeScript

## 🚀 Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)

## 📦 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🏗 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and theme variables
│   ├── layout.tsx           # Root layout with metadata and structured data
│   └── page.tsx             # Main page component
├── components/
│   ├── ui/                  # shadcn/ui components
│   │   ├── button.tsx
│   │   └── card.tsx
│   ├── social-links.tsx     # Social media links component
│   ├── structured-data.tsx  # JSON-LD structured data
│   └── theme-toggle.tsx     # Light/dark theme toggle
└── lib/
    └── utils.ts             # Utility functions
```

## 🎨 Design System

The website uses a modern design system with:
- **Colors**: Neutral palette with support for light/dark themes
- **Typography**: Geist font family for clean, professional text
- **Components**: shadcn/ui components for consistency
- **Spacing**: Consistent spacing scale using Tailwind CSS
- **Responsive**: Mobile-first responsive design

## 📱 Business Verification

This website includes all necessary elements for Meta Business Verification:
- Business name, ID (IČO), address, and email in metadata
- Structured data (JSON-LD) for automated verification
- Semantic HTML with proper contact information
- Professional appearance and functionality

## ⚙️ Configuration

### Environment Variables

The website uses environment variables to configure Credly badges:

1. **Copy the example environment file**:
   ```bash
   cp .env.example .env.local
   ```

2. **Configure your Credly badges**:
   ```bash
   # Number of badges to display (1-10)
   NEXT_PUBLIC_CREDLY_BADGE_COUNT=1

   # Badge IDs from your Credly profile
   NEXT_PUBLIC_CREDLY_BADGE_ID_1=your-badge-id-here
   NEXT_PUBLIC_CREDLY_BADGE_ID_2=your-second-badge-id-here
   # Add more as needed...
   ```

3. **Find your Credly Badge ID**:
   - Go to your Credly profile
   - Click on a badge
   - Copy the ID from the URL: `https://www.credly.com/badges/[BADGE-ID-HERE]`

## 🌐 Deployment

The website is ready for deployment on any platform that supports Next.js:
- **Vercel** (recommended)
- **Netlify**
- **AWS Amplify**
- **Self-hosted**

**Note**: Remember to set your environment variables in your deployment platform's settings.

## 📄 License

This project is created for Arthur Locke's professional use.
